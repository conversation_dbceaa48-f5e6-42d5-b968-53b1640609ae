-- Migration to convert billing and shipping addresses from single strings to arrays
-- This migration maintains backward compatibility by preserving existing data

-- First, add new array columns
ALTER TABLE "additional_user_info" ADD COLUMN "billingAddresses" TEXT[];
ALTER TABLE "additional_user_info" ADD COLUMN "shippingAddresses" TEXT[];

-- Migrate existing data: convert single address strings to arrays
UPDATE "additional_user_info" 
SET "billingAddresses" = CASE 
    WHEN "billingAddress" IS NOT NULL AND "billingAddress" != '' 
    THEN ARRAY["billingAddress"]
    ELSE ARRAY[]::TEXT[]
END;

UPDATE "additional_user_info" 
SET "shippingAddresses" = CASE 
    WHEN "shippingAddress" IS NOT NULL AND "shippingAddress" != '' 
    THEN ARRAY["shippingAddress"]
    ELSE ARRAY[]::TEXT[]
END;

-- Set default empty arrays for null values
UPDATE "additional_user_info" 
SET "billingAddresses" = ARRAY[]::TEXT[]
WHERE "billingAddresses" IS NULL;

UPDATE "additional_user_info" 
SET "shippingAddresses" = ARRAY[]::TEXT[]
WHERE "shippingAddresses" IS NULL;

-- Make the new columns non-nullable with default empty arrays
ALTER TABLE "additional_user_info" ALTER COLUMN "billingAddresses" SET NOT NULL;
ALTER TABLE "additional_user_info" ALTER COLUMN "billingAddresses" SET DEFAULT '{}';
ALTER TABLE "additional_user_info" ALTER COLUMN "shippingAddresses" SET NOT NULL;
ALTER TABLE "additional_user_info" ALTER COLUMN "shippingAddresses" SET DEFAULT '{}';

-- Keep the old columns for backward compatibility (they will be removed in a future migration)
-- This allows for a gradual transition and rollback if needed
